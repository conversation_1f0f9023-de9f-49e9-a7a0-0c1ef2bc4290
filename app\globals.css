@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Cursor Styles */
* {
  cursor: none !important;
}

.custom-cursor {
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

.cursor-trail {
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Smooth cursor animations */
@keyframes cursorPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

@keyframes cursorGlow {
  0%, 100% {
    box-shadow: 0 0 10px rgba(255, 101, 0, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(255, 101, 0, 0.6);
  }
}

@keyframes socialGlow {
  0%, 100% {
    box-shadow: 0 0 10px rgba(0, 255, 150, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 255, 150, 0.6);
  }
}

.cursor-pulse {
  animation: cursorPulse 2s ease-in-out infinite;
}

.cursor-glow {
  animation: cursorGlow 1.5s ease-in-out infinite;
}

.cursor-social-glow {
  animation: socialGlow 1.5s ease-in-out infinite;
}

/* Enhanced interactive elements */
.btn-primary, .btn-secondary {
  transition: all 0.3s ease;
}

.btn-primary:hover, .btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(255, 101, 0, 0.3);
}

/* Card hover effects */
.project-card, .skill-card, .card {
  transition: all 0.3s ease;
}

.project-card:hover, .skill-card:hover, .card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

/* Hide default cursor on interactive elements */
button, a, input, textarea, select, [role="button"] {
  cursor: none !important;
}

/* Ensure cursor works on mobile (hide on touch devices) */
@media (hover: none) and (pointer: coarse) {
  .custom-cursor,
  .cursor-trail {
    display: none !important;
  }

  * {
    cursor: auto !important;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 0%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
  }
}

@layer components {
  .section-padding {
    @apply px-4 sm:px-6 lg:px-8 py-8 sm:py-12 lg:py-16;
  }

  .container-custom {
    @apply max-w-7xl mx-auto;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-primary-orange to-primary-blue bg-clip-text text-transparent;
  }

  .btn-primary {
    @apply bg-primary-orange hover:bg-primary-orange/90 text-white px-4 py-2 sm:px-6 sm:py-3 rounded-lg font-medium transition-all duration-300 hover:scale-105 hover:shadow-lg text-sm sm:text-base;
  }

  .btn-secondary {
    @apply border-2 border-primary-blue text-primary-blue hover:bg-primary-blue hover:text-white px-4 py-2 sm:px-6 sm:py-3 rounded-lg font-medium transition-all duration-300 text-sm sm:text-base;
  }

  .card-hover {
    @apply transition-all duration-300 hover:scale-105 hover:shadow-xl;
  }

  .page-transition {
    @apply animate-in fade-in-0 duration-500;
  }
}

* {
  @apply border-border;
}

body {
  @apply bg-black text-foreground;
  font-feature-settings: "rlig" 1, "calt" 1;
}

html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-900;
}

::-webkit-scrollbar-thumb {
  @apply bg-primary-orange rounded;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-primary-orange/80;
}

/* Loading animations */
@keyframes pulse-slow {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse-slow {
  animation: pulse-slow 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}



/* Mobile optimizations */
@media (max-width: 640px) {
  .section-padding {
    @apply px-4 py-8;
  }

  .text-responsive {
    @apply text-2xl sm:text-3xl md:text-4xl lg:text-5xl;
  }
}

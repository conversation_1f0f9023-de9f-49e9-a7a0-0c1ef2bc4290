# Rahul Seervi - Portfolio Website

A modern, responsive portfolio website built with Next.js, showcasing my journey as a B.Tech CSE student specializing in Big Data Analytics.

## 🚀 Live Demo

[Visit Portfolio Website](https://your-portfolio-url.vercel.app)

## 📋 Features

- **Modern Design** - Clean, professional interface with dark theme
- **Responsive Layout** - Optimized for all devices (mobile, tablet, desktop)
- **Interactive Elements** - Smooth animations and hover effects
- **Fast Performance** - Built with Next.js for optimal loading speeds
- **SEO Optimized** - Meta tags and structured data for better search visibility

## 🛠️ Tech Stack

- **Framework:** Next.js 15.2.4
- **Styling:** Tailwind CSS
- **Language:** TypeScript
- **Icons:** Lucide React
- **Deployment:** Vercel
- **Package Manager:** pnpm

## 📱 Sections

- **Home** - Hero section with introduction and quick stats
- **About** - Personal journey and background
- **Skills** - Technical expertise and proficiency levels
- **Projects** - Featured work and accomplishments
- **Contact** - Multiple ways to get in touch

## 🎨 Design Features

- Custom color scheme with orange (#ff6500) and blue (#1e3e62) accents
- Gradient text effects and backgrounds
- Loading animations for each page
- Interactive buttons and cards
- Smooth page transitions

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- pnpm (recommended) or npm

### Installation

1. Clone the repository
```bash
git clone https://github.com/TheCreativeCodeFlow/rahul-seervi-portfolio.git
cd rahul-seervi-portfolio
```

2. Install dependencies
```bash
pnpm install
```

3. Run the development server
```bash
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

### Build for Production

```bash
pnpm build
pnpm start
```

## 📁 Project Structure

```
├── app/                    # Next.js app directory
│   ├── about/             # About page
│   ├── contact/           # Contact page
│   ├── projects/          # Projects page
│   ├── skills/            # Skills page
│   ├── api/               # API routes
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # Reusable components
│   ├── ui/               # UI components
│   ├── Navbar.tsx        # Navigation component
│   ├── Footer.tsx        # Footer component
│   └── LoadingScreens.tsx # Loading animations
├── public/               # Static assets
│   └── images/           # Images and logos
└── lib/                  # Utility functions
```

## 🎯 Key Highlights

- **15+ Projects** completed across various technologies
- **10+ Technologies** mastered including Java, React, MongoDB
- **2+ Years** of development experience
- Focus on **Big Data Analytics** and modern web technologies

## 📞 Contact

- **Email:** <EMAIL>
- **Business Email:** <EMAIL>
- **LinkedIn:** [Rahul Seervi](https://www.linkedin.com/in/rahul-seervi-a14440289/)
- **GitHub:** [TheCreativeCodeFlow](https://github.com/TheCreativeCodeFlow)

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🙏 Acknowledgments

- Built with [Next.js](https://nextjs.org/)
- Styled with [Tailwind CSS](https://tailwindcss.com/)
- Icons from [Lucide](https://lucide.dev/)
- Deployed on [Vercel](https://vercel.com/)

---

⭐ Star this repository if you found it helpful!
